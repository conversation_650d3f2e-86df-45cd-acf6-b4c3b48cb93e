import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:toii_social/cubit/auth/profile/profile_cubit.dart';
import 'package:toii_social/gen/assets.gen.dart';
import 'package:toii_social/model/user/user_model.dart';
import 'package:toii_social/router/app_router.dart';
import 'package:toii_social/screen/user_profile/widget/profile_card.dart';
import 'package:toii_social/screen/user_profile/widget/user_profile_more_actions_bottom_sheet.dart';
import 'package:toii_social/screen/user_profile/widget/user_profile_post_list.dart';
import 'package:toii_social/widget/colors/colors.dart';
import 'package:toii_social/widget/colors/text_style.dart';

class UserProfileScreen extends StatefulWidget {
  const UserProfileScreen({super.key, this.user});
  final UserModel? user;

  @override
  State<UserProfileScreen> createState() => _UserProfileScreenState();
}

class _UserProfileScreenState extends State<UserProfileScreen> {
  bool isMyProfile = false;
  @override
  void initState() {
    isMyProfile =
        widget.user?.id == GetIt.instance<ProfileCubit>().state.userModel?.id;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final themeData = context.themeData;
    return Scaffold(
      backgroundColor: themeData.neutral100,
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            expandedHeight: MediaQuery.of(context).size.height,
            pinned: true,
            stretch: true,
            backgroundColor: Colors.transparent,
            elevation: 0,
            leading:
                isMyProfile
                    ? SizedBox.shrink()
                    : Container(
                      margin: const EdgeInsets.all(8),
                      child: Material(
                        color: themeData.neutral400.withAlpha(200),
                        borderRadius: BorderRadius.circular(16),
                        child: InkWell(
                          borderRadius: BorderRadius.circular(8),
                          onTap: () => Navigator.of(context).pop(),
                          child: SizedBox(
                            width: 35,
                            height: 45,
                            child: Icon(
                              Icons.chevron_left,
                              color: Colors.white,
                              size: 24,
                            ),
                          ),
                        ),
                      ),
                    ),
            title: Text(
              widget.user?.username ?? widget.user?.fullName ?? '-',
              style: titleLarge.copyWith(color: themeData.neutral50),
            ),
            flexibleSpace: FlexibleSpaceBar(
              stretchModes: const [
                StretchMode.zoomBackground,
                StretchMode.fadeTitle,
              ],
              background: Stack(
                fit: StackFit.expand,
                children: [
                  Assets.images.defaultBackground.image(fit: BoxFit.cover),
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.black.withOpacity(0.25),
                          Colors.transparent,
                        ],
                      ),
                    ),
                  ),

                  SafeArea(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Align(
                          alignment: Alignment.bottomCenter,
                          child: Padding(
                            padding: const EdgeInsets.only(bottom: 0),
                            child: ProfileCard(user: widget.user),
                          ),
                        ),
                        const SizedBox(height: 12),
                        SafeArea(
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              vertical: 16,
                              horizontal: 20,
                            ),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(24),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.03),
                                  blurRadius: 8,
                                  offset: Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Column(
                              children: [
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Assets.icons.icScrollDown.svg(),
                                    const SizedBox(width: 16),
                                    Text(
                                      'Scroll down to see more',
                                      style: TextStyle(
                                        color: Colors.grey[700],
                                        fontSize: 16,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                  ],
                                ),

                                SizedBox(
                                  height:
                                      (isMyProfile
                                          ? MediaQuery.of(
                                            context,
                                          ).padding.bottom
                                          : 0) +
                                      98,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              titlePadding: const EdgeInsets.only(left: 56, bottom: 16),
            ),
            actions: [
              if (!isMyProfile)
                IconButton(
                  icon: Icon(Icons.more_horiz, color: themeData.neutral50),
                  onPressed: () {
                    showUserProfileMoreActionsBottomSheet(
                      context: context,
                      user: widget.user,
                    );
                  },
                ),
              if (isMyProfile)
                GestureDetector(
                  onTap: () {
                    context.push(RouterEnums.settingProfile.routeName);
                  },
                  child: Assets.icons.icSetting.svg(),
                ),
            ],
          ),
          // Tabs + Post List
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.only(top: 24),
              child: UserProfilePostList(user: widget.user),
            ),
          ),
        ],
      ),
    );
  }
}
