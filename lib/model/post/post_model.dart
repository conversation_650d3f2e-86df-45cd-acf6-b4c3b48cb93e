import 'package:json_annotation/json_annotation.dart';
import 'package:toii_social/model/post/reaction_model.dart';
import 'package:toii_social/model/user/user_model.dart';

part 'post_model.g.dart';

@JsonSerializable()
class PostResponseDataModel {
  final List<PostModel> posts;
  final int total;

  PostResponseDataModel({required this.posts, this.total = 0});

  factory PostResponseDataModel.fromJson(Map<String, dynamic> json) =>
      _$PostResponseDataModelFromJson(json);
  Map<String, dynamic> toJson() => _$PostResponseDataModelToJson(this);
}

@JsonSerializable()
class PostModel {
  const PostModel({
    required this.id,
    this.user,
    required this.content,
    this.mediaKeys = const [],
    this.mediaUrls = const [],
    this.mediaDetails = const [],
    required this.reposts,
    required this.comments,
    this.reactions,
    this.privacy,
    this.createdAt,
    this.updatedAt,
    this.isRepost,
  });

  final String id;
  final UserModel? user;
  @JsonKey(defaultValue: "")
  final String content;

  @JsonKey(name: 'media_keys')
  final List<String> mediaKeys;

  @JsonKey(name: 'media_urls')
  final List<String> mediaUrls;

  @JsonKey(name: 'media_details')
  final List<MediaDetailsModel> mediaDetails;

  @JsonKey(defaultValue: 0)
  final int reposts;
  @JsonKey(defaultValue: 0)
  final int comments;
  final ReactionGroupModel? reactions;
  final String? privacy;

  @JsonKey(name: 'created_at')
  final String? createdAt;

  @JsonKey(name: 'updated_at')
  final String? updatedAt;

  @JsonKey(name: 'is_repost')
  final bool? isRepost;
  
  @JsonKey(name: 'original_post')
  final PostModel? originalPost;
 
  factory PostModel.fromJson(Map<String, dynamic> json) =>
      _$PostModelFromJson(json);

  Map<String, dynamic> toJson() => _$PostModelToJson(this);

  PostModel copyWith({
    String? id,
    UserModel? user,
    String? content,
    List<String>? mediaKeys,
    List<String>? mediaUrls,
    int? reposts,
    int? comments,
    ReactionGroupModel? reactions,
    String? privacy,
    String? createdAt,
    String? updatedAt,
    bool? isRepost,
  }) {
    return PostModel(
      id: id ?? this.id,
      user: user,
      content: content ?? this.content,
      mediaKeys: mediaKeys ?? this.mediaKeys,
      mediaUrls: mediaUrls ?? this.mediaUrls,
      reposts: reposts ?? this.reposts,
      comments: comments ?? this.comments,
      reactions: reactions ?? this.reactions,
      privacy: privacy ?? this.privacy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isRepost: isRepost ?? this.isRepost,
    );
  }

  int get actionlikes {
    if (reactions == null) return 0;
    // todo sửa enum action sau
    return reactions!.reactions
            ?.where((reaction) => reaction.type?.toLowerCase() == 'love')
            .firstOrNull
            ?.count ??
        0;
  }
}

extension PostModelExtension on PostModel {
  bool get isLiked {
    if (reactions == null) return false;
    return reactions!.userReactions?.contains('love') ?? false;
    // todo sửa enum action sau
  }

  String get getViewLike {
    if (actionlikes == 0) return "";
    if (actionlikes >= 1000)
      return "${(actionlikes / 1000).toStringAsFixed(1)}K";
    return actionlikes.toString();
  }

  String get getViewComment {
    if (comments == 0) return "";
    if (comments >= 1000) return "${(comments / 1000).toStringAsFixed(1)}K";
    return comments.toString();
  }

  String get getViewRepost {
    if (reposts == 0) return "";
    if (reposts >= 1000) return "${(reposts / 1000).toStringAsFixed(1)}K";
    return reposts.toString();
  }
}

@JsonSerializable()
class CreatePostRequestModel {
  const CreatePostRequestModel({
    this.content,
    required this.privacy,
    this.mediaKeys = const [],
  });
  final String? content;

  @JsonKey(name: 'media_keys')
  final List<String> mediaKeys;

  final String privacy;

  factory CreatePostRequestModel.fromJson(Map<String, dynamic> json) =>
      _$CreatePostRequestModelFromJson(json);

  Map<String, dynamic> toJson() => _$CreatePostRequestModelToJson(this);
}

@JsonSerializable()
class MediaDetailsModel {
  String? id;
  @JsonKey(name: 'user_id')
  String? userId;
  String? type;
  @JsonKey(name: 'file_name')
  String? fileName;
  String? bucket;
  String? key;
  @JsonKey(name: 's3_url')
  String? s3Url;
  @JsonKey(name: 'cdn_url')
  String? cdnUrl;
  int? size;
  @JsonKey(name: 'content_type')
  String? contentType;
  @JsonKey(name: 'image_variants')
  List<ImageVariants> imageVariants;
  @JsonKey(name: 'original_width')
  int? originalWidth;
  @JsonKey(name: 'original_height')
  int? originalHeight;
  @JsonKey(name: 'created_at')
  String? createdAt;
  @JsonKey(name: 'updated_at')
  String? updatedAt;

  MediaDetailsModel({
    this.id,
    this.userId,
    this.type,
    this.fileName,
    this.bucket,
    this.key,
    this.s3Url,
    this.cdnUrl,
    this.size,
    this.contentType,
    this.imageVariants = const [],
    this.originalWidth,
    this.originalHeight,
    this.createdAt,
    this.updatedAt,
  });
  factory MediaDetailsModel.fromJson(Map<String, dynamic> json) =>
      _$MediaDetailsModelFromJson(json);

  Map<String, dynamic> toJson() => _$MediaDetailsModelToJson(this);
}

@JsonSerializable()
class ImageVariants {
  String? size;
  int? width;
  int? height;
  @JsonKey(name: 's3_url')
  String? s3Url;
  @JsonKey(name: 'cdn_url')
  String? cdnUrl;
  int? fileSize;

  ImageVariants({
    this.size,
    this.width,
    this.height,
    this.s3Url,
    this.cdnUrl,
    this.fileSize,
  });
  factory ImageVariants.fromJson(Map<String, dynamic> json) =>
      _$ImageVariantsFromJson(json);
  Map<String, dynamic> toJson() => _$ImageVariantsToJson(this);
}
